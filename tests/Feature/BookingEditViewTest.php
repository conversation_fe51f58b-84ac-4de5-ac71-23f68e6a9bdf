<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\BookingController::class)]
class BookingEditViewTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->adminUser = User::factory()->create(['role' => 'admin']);
        $this->employee = User::factory()->create(['role' => 'employee']);
    }

    #[Test]
    public function booking_edit_page_loads_with_correct_layout(): void
    {
        // Create a field and booking
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
        ]);

        // Test as admin
        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(200);
        $response->assertViewIs('bookings.edit');
        $response->assertViewHas('booking', $booking);
        $response->assertViewHas('fields');

        // Check that the page contains Bootstrap classes (not Tailwind)
        $response->assertSee('form-control');
        $response->assertSee('btn btn-primary');
        $response->assertSee('card custom-card');

        // Check that it doesn't contain Tailwind classes
        $response->assertDontSee('bg-gray-50');
        $response->assertDontSee('text-gray-700');
        $response->assertDontSee('border-gray-300');
    }

    #[Test]
    public function booking_owner_can_access_edit_page(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
        ]);

        $response = $this->actingAs($this->employee)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(200);
        $response->assertViewIs('bookings.edit');
    }

    #[Test]
    public function unauthorized_user_cannot_access_edit_page(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
        ]);

        $otherUser = User::factory()->create(['role' => 'employee']);

        $response = $this->actingAs($otherUser)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(403);
    }

    #[Test]
    public function edit_form_contains_required_fields(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(200);

        // Check for form fields
        $response->assertSee('name="field_id"', false);
        $response->assertSee('name="booking_date"', false);
        $response->assertSee('name="start_time"', false);
        $response->assertSee('name="duration_hours"', false);
        $response->assertSee('name="customer_name"', false);
        $response->assertSee('name="customer_email"', false);
        $response->assertSee('name="customer_phone"', false);
        $response->assertSee('name="special_requests"', false);

        // Check for CSRF token
        $response->assertSee('name="_token"', false);
        $response->assertSee('name="_method"', false);
        $response->assertSee('value="PUT"', false);
    }

    #[Test]
    public function edit_form_shows_current_booking_data(): void
    {
        $field = Field::factory()->create(['name' => 'Test Soccer Field']);
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(200);

        // Check that current data is displayed
        $response->assertSee('Test Soccer Field');
        $response->assertSee('John Doe');
        $response->assertSee('<EMAIL>');
        $response->assertSee('************');
        $response->assertSee($booking->status);
    }

    #[Test]
    public function javascript_functions_are_included(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(200);

        // Check for JavaScript functions
        $response->assertSee('function updateFieldInfo()');
        $response->assertSee('function calculateCost()');
        $response->assertSee('DOMContentLoaded');
    }

    #[Test]
    public function past_bookings_cannot_be_edited(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->subDays(1)->format('Y-m-d'), // Past date
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertRedirect();
        $response->assertSessionHas('error', 'This booking cannot be edited.');
    }

    #[Test]
    public function completed_bookings_cannot_be_edited(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Completed',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertRedirect();
        $response->assertSessionHas('error', 'This booking cannot be edited.');
    }
}
