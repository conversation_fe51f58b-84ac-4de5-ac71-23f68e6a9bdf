<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Models\Field::class)]
#[CoversClass(\App\Models\Utility::class)]
class FieldUtilityRelationshipTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected $employee;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->employee = User::factory()->create(['role' => 'employee']);
    }

    #[Test]
    public function admin_can_create_field_with_utilities()
    {
        // Create some utilities
        $utilities = Utility::factory()->count(3)->create(['is_active' => true]);
        $utilityIds = $utilities->pluck('id')->toArray();

        $fieldData = [
            'name' => 'Test Field with Utilities',
            'type' => 'Soccer',
            'description' => 'A test field with utilities',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
            'utilities' => $utilityIds,
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/fields', $fieldData);

        $response->assertRedirect('/admin/fields');

        // Verify field was created
        $field = Field::where('name', 'Test Field with Utilities')->first();
        $this->assertNotNull($field);

        // Verify utilities are attached
        $this->assertEquals(3, $field->utilities->count());
        foreach ($utilities as $utility) {
            $this->assertTrue($field->utilities->contains($utility));
        }
    }

    #[Test]
    public function admin_can_update_field_utilities()
    {
        // Create field and utilities
        $field = Field::factory()->create();
        $originalUtilities = Utility::factory()->count(2)->create(['is_active' => true]);
        $newUtilities = Utility::factory()->count(2)->create(['is_active' => true]);

        // Attach original utilities
        $field->utilities()->attach($originalUtilities);

        // Update with new utilities
        $updateData = [
            'name' => $field->name,
            'type' => $field->type,
            'description' => $field->description,
            'hourly_rate' => $field->hourly_rate,
            'capacity' => $field->capacity,
            'status' => $field->status,
            'utilities' => $newUtilities->pluck('id')->toArray(),
        ];

        $response = $this->actingAs($this->admin)
            ->put("/admin/fields/{$field->id}", $updateData);

        $response->assertRedirect("/admin/fields/{$field->id}");

        // Verify utilities were updated
        $field->refresh();
        $this->assertEquals(2, $field->utilities->count());

        // Check that new utilities are attached and old ones are not
        foreach ($newUtilities as $utility) {
            $this->assertTrue($field->utilities->contains($utility));
        }
        foreach ($originalUtilities as $utility) {
            $this->assertFalse($field->utilities->contains($utility));
        }
    }

    #[Test]
    public function admin_can_remove_all_utilities_from_field()
    {
        // Create field with utilities
        $field = Field::factory()->create();
        $utilities = Utility::factory()->count(3)->create(['is_active' => true]);
        $field->utilities()->attach($utilities);

        // Update field without utilities
        $updateData = [
            'name' => $field->name,
            'type' => $field->type,
            'description' => $field->description,
            'hourly_rate' => $field->hourly_rate,
            'capacity' => $field->capacity,
            'status' => $field->status,
            // utilities not included (empty)
        ];

        $response = $this->actingAs($this->admin)
            ->put("/admin/fields/{$field->id}", $updateData);

        $response->assertRedirect("/admin/fields/{$field->id}");

        // Verify all utilities were removed
        $field->refresh();
        $this->assertEquals(0, $field->utilities->count());
    }

    #[Test]
    public function field_show_page_displays_utilities()
    {
        // Create field with utilities
        $field = Field::factory()->create();
        $utilities = Utility::factory()->count(2)->create([
            'is_active' => true,
            'icon_class' => 'ri-test-line',
        ]);
        $field->utilities()->attach($utilities);

        $response = $this->actingAs($this->admin)
            ->get("/admin/fields/{$field->id}");

        $response->assertStatus(200);
        $response->assertSee('Available Utilities');

        foreach ($utilities as $utility) {
            $response->assertSee($utility->name);
            $response->assertSee($utility->icon_class);
        }
    }

    #[Test]
    public function field_show_page_displays_no_utilities_message()
    {
        // Create field without utilities
        $field = Field::factory()->create();

        $response = $this->actingAs($this->admin)
            ->get("/admin/fields/{$field->id}");

        $response->assertStatus(200);
        $response->assertSee('Available Utilities');
        $response->assertSee('No utilities specified');
    }

    #[Test]
    public function field_edit_form_shows_utility_checkboxes()
    {
        // Create field and utilities
        $field = Field::factory()->create();
        $utilities = Utility::factory()->count(3)->create(['is_active' => true]);
        $field->utilities()->attach($utilities->take(2)); // Attach first 2

        $response = $this->actingAs($this->admin)
            ->get("/admin/fields/{$field->id}/edit");

        $response->assertStatus(200);
        $response->assertSee('Available Utilities');

        foreach ($utilities as $utility) {
            $response->assertSee($utility->name);
            $response->assertSee("utility_{$utility->id}");
        }
    }

    #[Test]
    public function field_create_form_shows_utility_checkboxes()
    {
        // Create some utilities
        $utilities = Utility::factory()->count(3)->create(['is_active' => true]);

        $response = $this->actingAs($this->admin)
            ->get('/admin/fields/create');

        $response->assertStatus(200);
        $response->assertSee('Available Utilities');

        foreach ($utilities as $utility) {
            $response->assertSee($utility->name);
            $response->assertSee("utility_{$utility->id}");
        }
    }

    #[Test]
    public function only_active_utilities_are_shown_in_forms()
    {
        // Create active and inactive utilities
        $activeUtility = Utility::factory()->create(['is_active' => true, 'name' => 'Active Utility']);
        $inactiveUtility = Utility::factory()->create(['is_active' => false, 'name' => 'Inactive Utility']);

        $response = $this->actingAs($this->admin)
            ->get('/admin/fields/create');

        $response->assertStatus(200);
        $response->assertSee('Active Utility');
        $response->assertDontSee('Inactive Utility');
    }

    #[Test]
    public function field_utility_validation_works()
    {
        $fieldData = [
            'name' => 'Test Field',
            'type' => 'Soccer',
            'description' => 'A test field',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
            'utilities' => [999], // Non-existent utility ID
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/fields', $fieldData);

        $response->assertSessionHasErrors('utilities.0');
    }

    #[Test]
    public function field_utility_relationship_is_many_to_many()
    {
        // Create field and utilities
        $field1 = Field::factory()->create();
        $field2 = Field::factory()->create();
        $utility = Utility::factory()->create(['is_active' => true]);

        // Attach same utility to both fields
        $field1->utilities()->attach($utility);
        $field2->utilities()->attach($utility);

        // Verify many-to-many relationship
        $this->assertTrue($field1->utilities->contains($utility));
        $this->assertTrue($field2->utilities->contains($utility));
        $this->assertTrue($utility->fields->contains($field1));
        $this->assertTrue($utility->fields->contains($field2));
        $this->assertEquals(2, $utility->fields->count());
    }

    #[Test]
    public function deleting_utility_removes_field_relationships()
    {
        // Create field and utility
        $field = Field::factory()->create();
        $utility = Utility::factory()->create(['is_active' => true]);
        $field->utilities()->attach($utility);

        // Verify relationship exists
        $this->assertEquals(1, $field->utilities->count());

        // Delete utility (soft delete)
        $utility->delete();

        // Verify relationship is removed (due to cascade delete in migration)
        $field->refresh();
        $this->assertEquals(0, $field->utilities->count());
    }
}
