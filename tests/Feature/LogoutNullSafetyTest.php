<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\Auth\AuthenticatedSessionController::class)]
class LogoutNullSafetyTest extends TestCase
{
    use RefreshDatabase;

    protected User $clientUser;

    protected User $employee;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->clientUser = User::factory()->create([
            'name' => 'Client User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        $this->employee = User::factory()->create([
            'name' => 'Employee',
            'email' => '<EMAIL>',
            'role' => 'employee',
        ]);

        $this->adminUser = User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);
    }

    #[Test]
    public function client_user_can_logout_without_null_reference_error()
    {
        // Login as client
        $this->actingAs($this->clientUser);

        // Verify user is authenticated
        $this->assertAuthenticated();

        // Access a page that includes header/sidebar (to trigger role checks)
        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);

        // Perform logout - should not throw null reference error
        $response = $this->post(route('logout'));

        // Should redirect successfully
        $response->assertStatus(302);
        $response->assertRedirect('/');

        // Verify user is logged out
        $this->assertGuest();
    }

    #[Test]
    public function normal_user_can_logout_without_null_reference_error()
    {
        // Login as employee
        $this->actingAs($this->employee);

        // Verify user is authenticated
        $this->assertAuthenticated();

        // Access a page that includes header/sidebar (to trigger role checks)
        $response = $this->get(route('employee.dashboard'));
        $response->assertStatus(200);

        // Perform logout - should not throw null reference error
        $response = $this->post(route('logout'));

        // Should redirect successfully
        $response->assertStatus(302);
        $response->assertRedirect('/');

        // Verify user is logged out
        $this->assertGuest();
    }

    #[Test]
    public function admin_user_can_logout_without_null_reference_error()
    {
        // Login as admin
        $this->actingAs($this->adminUser);

        // Verify user is authenticated
        $this->assertAuthenticated();

        // Access a page that includes header/sidebar (to trigger role checks)
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);

        // Perform logout - should not throw null reference error
        $response = $this->post(route('logout'));

        // Should redirect successfully
        $response->assertStatus(302);
        $response->assertRedirect('/');

        // Verify user is logged out
        $this->assertGuest();
    }

    #[Test]
    public function header_template_handles_null_user_during_logout_gracefully()
    {
        // Login as employee (who should see reservation buttons)
        $this->actingAs($this->employee);

        // Access dashboard to load header template
        $response = $this->get(route('employee.dashboard'));
        $response->assertStatus(200);

        // Verify reservation button is visible for employee
        $response->assertSee('New Reservation');

        // Logout should work without null reference errors
        $response = $this->post(route('logout'));
        $response->assertStatus(302);
        $this->assertGuest();
    }

    #[Test]
    public function sidebar_template_handles_null_user_during_logout_gracefully()
    {
        // Login as admin (who should see admin features)
        $this->actingAs($this->adminUser);

        // Access dashboard to load sidebar template
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);

        // Logout should work without null reference errors
        $response = $this->post(route('logout'));
        $response->assertStatus(302);
        $this->assertGuest();
    }

    #[Test]
    public function client_user_sees_clean_header_without_reservation_buttons()
    {
        // Login as client
        $this->actingAs($this->clientUser);

        // Access dashboard
        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);

        // Client should NOT see reservation buttons in header
        $response->assertDontSee('New Reservation');
        $response->assertDontSee('My Reservations');

        // But should see appropriate client links
        $response->assertSee('Profile');
        $response->assertSee('My Profile');
    }

    #[Test]
    public function normal_user_sees_reservation_buttons_in_header()
    {
        // Login as employee
        $this->actingAs($this->employee);

        // Access dashboard
        $response = $this->get(route('employee.dashboard'));
        $response->assertStatus(200);

        // employee should see reservation buttons
        $response->assertSee('New Reservation');
        $response->assertSee('My Reservations');
        $response->assertSee('Features');
    }

    #[Test]
    public function admin_user_sees_reservation_buttons_in_header()
    {
        // Login as admin
        $this->actingAs($this->adminUser);

        // Access dashboard
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);

        // Admin should see reservation buttons
        $response->assertSee('New Reservation');
        $response->assertSee('My Reservations');
        $response->assertSee('Features');
    }

    #[Test]
    public function client_user_sees_clean_sidebar_without_fpmp_features()
    {
        // Login as client
        $this->actingAs($this->clientUser);

        // Access dashboard
        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);

        $content = $response->getContent();

        // Client should NOT see FPMP reservation features in sidebar
        $this->assertStringNotContainsString('FPMP', $content);
        $this->assertStringNotContainsString('Quick Reserve', $content);
        $this->assertStringNotContainsString('FPMP Reservations', $content);

        // But should see basic navigation
        $this->assertStringContainsString('Dashboard', $content);
        $this->assertStringContainsString('Calendar', $content);
        $this->assertStringContainsString('Profile', $content);
    }

    #[Test]
    public function normal_user_sees_fpmp_features_in_sidebar()
    {
        // Login as employee
        $this->actingAs($this->employee);

        // Access dashboard
        $response = $this->get(route('employee.dashboard'));
        $response->assertStatus(200);

        $content = $response->getContent();

        // employee should see FPMP features
        $this->assertStringContainsString('FPMP', $content);
        $this->assertStringContainsString('Quick Reserve', $content);
        $this->assertStringContainsString('FPMP Reservations', $content);
    }

    #[Test]
    public function admin_user_sees_all_features_in_sidebar()
    {
        // Login as admin
        $this->actingAs($this->adminUser);

        // Access dashboard
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);

        $content = $response->getContent();

        // Admin should see all features including FPMP and admin features
        $this->assertStringContainsString('FPMP', $content);
        $this->assertStringContainsString('Quick Reserve', $content);
        $this->assertStringContainsString('FPMP Reservations', $content);
        $this->assertStringContainsString('Administration', $content);
    }

    #[Test]
    public function multiple_logout_attempts_dont_cause_null_reference_errors()
    {
        // Login as client
        $this->actingAs($this->clientUser);

        // First logout
        $response = $this->post(route('logout'));
        $response->assertStatus(302);
        $this->assertGuest();

        // Second logout attempt (user already logged out)
        $response = $this->post(route('logout'));
        $response->assertStatus(302); // Should still redirect, not error

        // Verify still logged out
        $this->assertGuest();
    }

    #[Test]
    public function header_template_null_safe_operators_work_correctly()
    {
        // Test that null-safe operators don't cause issues when user exists
        $this->actingAs($this->employee);

        $response = $this->get(route('employee.dashboard'));
        $response->assertStatus(200);

        // Should see reservation features for employee user
        $response->assertSee('New Reservation');

        // Now test with user (should not see reservation features)
        $this->actingAs($this->clientUser);

        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);

        // Should not see reservation features for user
        $response->assertDontSee('New Reservation');
    }

    #[Test]
    public function sidebar_template_null_safe_operators_work_correctly()
    {
        // Test with admin user (should see admin features)
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);

        $content = $response->getContent();
        $this->assertStringContainsString('Administration', $content);

        // Test with client user (should not see admin features)
        $this->actingAs($this->clientUser);

        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);

        $content = $response->getContent();
        $this->assertStringNotContainsString('Administration', $content);
    }
}
