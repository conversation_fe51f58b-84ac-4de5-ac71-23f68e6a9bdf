<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function login_with_email(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'employee',
        ]);

        $response = $this->post('/login', [
            'login' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($user);
    }

    #[Test]
    public function login_with_username(): void
    {
        $user = User::factory()->create([
            'username' => 'testuser',
            'password' => Hash::make('password'),
            'role' => 'employee',
        ]);

        $response = $this->post('/login', [
            'login' => 'testuser',
            'password' => 'password',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($user);
    }

    #[Test]
    public function admin_middleware_protection(): void
    {
        $employee = User::factory()->create(['role' => 'employee']);

        $this->actingAs($employee)
            ->get('/admin/users')
            ->assertStatus(403);
    }

    #[Test]
    public function admin_access_to_user_management(): void
    {
        $adminUser = User::factory()->create(['role' => 'admin']);

        $this->actingAs($adminUser)
            ->get('/admin/users')
            ->assertStatus(200);
    }

    #[Test]
    public function account_lockout_after_failed_attempts(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'employee',
        ]);

        // Make 5 failed login attempts
        for ($i = 0; $i < 5; $i++) {
            $this->post('/login', [
                'login' => '<EMAIL>',
                'password' => 'wrongpassword',
            ]);
        }

        $user->refresh();
        $this->assertTrue($user->isLocked());
        $this->assertEquals(5, $user->failed_login_attempts);
    }

    #[Test]
    public function role_based_dashboard_redirection(): void
    {
        $adminUser = User::factory()->create(['role' => 'admin']);
        $employeeUser = User::factory()->create(['role' => 'employee']);
        $userUser = User::factory()->create(['role' => 'user']);

        // Test admin redirection
        $this->actingAs($adminUser)
            ->get('/dashboard')
            ->assertRedirect('/admin/dashboard');

        // Test employee redirection
        $this->actingAs($employeeUser)
            ->get('/dashboard')
            ->assertRedirect('/employee/dashboard');

        // Test user redirection
        $this->actingAs($userUser)
            ->get('/dashboard')
            ->assertRedirect('/user/dashboard');
    }
}
