<?php

namespace Tests\Unit;

use App\Models\Field;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Models\Utility::class)]
class UtilityModelTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function utility_has_fillable_attributes()
    {
        $utility = new Utility;

        $fillable = $utility->getFillable();

        $this->assertContains('name', $fillable);
        $this->assertContains('description', $fillable);
        $this->assertContains('hourly_rate', $fillable);
        $this->assertContains('is_active', $fillable);
    }

    #[Test]
    public function utility_casts_is_active_to_boolean()
    {
        $utility = Utility::factory()->create(['is_active' => 1]);

        $this->assertIsBool($utility->is_active);
        $this->assertTrue($utility->is_active);
    }

    #[Test]
    public function utility_has_fields_relationship()
    {
        $utility = Utility::factory()->create();
        $field = Field::factory()->create();

        $utility->fields()->attach($field);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $utility->fields);
        $this->assertTrue($utility->fields->contains($field));
    }

    #[Test]
    public function active_scope_returns_only_active_utilities()
    {
        Utility::factory()->create(['is_active' => true]);
        Utility::factory()->create(['is_active' => false]);

        $activeUtilities = Utility::active()->get();

        $this->assertEquals(1, $activeUtilities->count());
        $this->assertTrue($activeUtilities->first()->is_active);
    }

    #[Test]
    public function inactive_scope_returns_only_inactive_utilities()
    {
        Utility::factory()->create(['is_active' => true]);
        Utility::factory()->create(['is_active' => false]);

        $inactiveUtilities = Utility::inactive()->get();

        $this->assertEquals(1, $inactiveUtilities->count());
        $this->assertFalse($inactiveUtilities->first()->is_active);
    }

    #[Test]
    public function display_name_attribute_includes_status()
    {
        $activeUtility = Utility::factory()->create([
            'name' => 'Test Utility',
            'is_active' => true,
        ]);

        $inactiveUtility = Utility::factory()->create([
            'name' => 'Test Utility 2',
            'is_active' => false,
        ]);

        $this->assertEquals('Test Utility', $activeUtility->display_name);
        $this->assertEquals('Test Utility 2 (Inactive)', $inactiveUtility->display_name);
    }

    #[Test]
    public function status_badge_class_returns_correct_classes()
    {
        $activeUtility = Utility::factory()->create(['is_active' => true]);
        $inactiveUtility = Utility::factory()->create(['is_active' => false]);

        $this->assertEquals('bg-success', $activeUtility->status_badge_class);
        $this->assertEquals('bg-danger', $inactiveUtility->status_badge_class);
    }

    #[Test]
    public function status_text_returns_correct_text()
    {
        $activeUtility = Utility::factory()->create(['is_active' => true]);
        $inactiveUtility = Utility::factory()->create(['is_active' => false]);

        $this->assertEquals('Active', $activeUtility->status_text);
        $this->assertEquals('Inactive', $inactiveUtility->status_text);
    }

    #[Test]
    public function can_be_deleted_returns_true_when_no_fields_use_utility()
    {
        $utility = Utility::factory()->create();

        $this->assertTrue($utility->canBeDeleted());
    }

    #[Test]
    public function can_be_deleted_returns_false_when_fields_use_utility()
    {
        $utility = Utility::factory()->create();
        $field = Field::factory()->create();
        $utility->fields()->attach($field);

        $this->assertFalse($utility->canBeDeleted());
    }

    #[Test]
    public function fields_count_attribute_returns_correct_count()
    {
        $utility = Utility::factory()->create();
        $fields = Field::factory()->count(3)->create();

        $utility->fields()->attach($fields);

        $this->assertEquals(3, $utility->fields_count);
    }

    #[Test]
    public function activate_method_sets_utility_to_active()
    {
        $utility = Utility::factory()->create(['is_active' => false]);

        $result = $utility->activate();

        $this->assertTrue($result);
        $this->assertTrue($utility->fresh()->is_active);
    }

    #[Test]
    public function deactivate_method_sets_utility_to_inactive()
    {
        $utility = Utility::factory()->create(['is_active' => true]);

        $result = $utility->deactivate();

        $this->assertTrue($result);
        $this->assertFalse($utility->fresh()->is_active);
    }

    #[Test]
    public function toggle_status_method_toggles_active_status()
    {
        $utility = Utility::factory()->create(['is_active' => true]);

        $result = $utility->toggleStatus();

        $this->assertTrue($result);
        $this->assertFalse($utility->fresh()->is_active);

        $utility->toggleStatus();
        $this->assertTrue($utility->fresh()->is_active);
    }

    #[Test]
    public function utility_uses_soft_deletes()
    {
        $utility = Utility::factory()->create();
        $utilityId = $utility->id;

        $utility->delete();

        $this->assertSoftDeleted('utilities', ['id' => $utilityId]);
        $this->assertNotNull($utility->fresh()->deleted_at);
    }

    #[Test]
    public function utility_name_is_required_for_mass_assignment()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        Utility::create([
            'description' => 'Test description',
            'hourly_rate' => 25.00,
            'is_active' => true,
        ]);
    }
}
