<?php

namespace Tests\Unit;

use App\Models\Field;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Models\Utility::class)]
class UtilityModelTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function utility_has_fillable_attributes()
    {
        $utility = new Utility;

        $fillable = $utility->getFillable();

        $this->assertContains('name', $fillable);
        $this->assertContains('description', $fillable);
        $this->assertContains('hourly_rate', $fillable);
        $this->assertContains('is_active', $fillable);
    }

    #[Test]
    public function utility_casts_is_active_to_boolean()
    {
        $utility = Utility::factory()->create(['is_active' => 1]);

        $this->assertIsBool($utility->is_active);
        $this->assertTrue($utility->is_active);
    }

    #[Test]
    public function utility_has_fields_relationship()
    {
        $utility = Utility::factory()->create();
        $field = Field::factory()->create();

        $utility->fields()->attach($field);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $utility->fields);
        $this->assertTrue($utility->fields->contains($field));
    }

    #[Test]
    public function active_scope_returns_only_active_utilities()
    {
        Utility::factory()->create(['is_active' => true]);
        Utility::factory()->create(['is_active' => false]);

        $activeUtilities = Utility::active()->get();

        $this->assertEquals(1, $activeUtilities->count());
        $this->assertTrue($activeUtilities->first()->is_active);
    }

    #[Test]
    public function inactive_scope_returns_only_inactive_utilities()
    {
        Utility::factory()->create(['is_active' => true]);
        Utility::factory()->create(['is_active' => false]);

        $inactiveUtilities = Utility::inactive()->get();

        $this->assertEquals(1, $inactiveUtilities->count());
        $this->assertFalse($inactiveUtilities->first()->is_active);
    }

    #[Test]
    public function display_name_attribute_includes_status()
    {
        $activeUtility = Utility::factory()->create([
            'name' => 'Test Utility',
            'is_active' => true,
        ]);

        $inactiveUtility = Utility::factory()->create([
            'name' => 'Test Utility 2',
            'is_active' => false,
        ]);

        $this->assertEquals('Test Utility', $activeUtility->display_name);
        $this->assertEquals('Test Utility 2 (Inactive)', $inactiveUtility->display_name);
    }

    #[Test]
    public function status_badge_class_returns_correct_classes()
    {
        $activeUtility = Utility::factory()->create(['is_active' => true]);
        $inactiveUtility = Utility::factory()->create(['is_active' => false]);

        $this->assertEquals('bg-success', $activeUtility->status_badge_class);
        $this->assertEquals('bg-danger', $inactiveUtility->status_badge_class);
    }

    #[Test]
    public function status_text_returns_correct_text()
    {
        $activeUtility = Utility::factory()->create(['is_active' => true]);
        $inactiveUtility = Utility::factory()->create(['is_active' => false]);

        $this->assertEquals('Active', $activeUtility->status_text);
        $this->assertEquals('Inactive', $inactiveUtility->status_text);
    }

    #[Test]
    public function can_be_deleted_returns_true_when_no_fields_use_utility()
    {
        $utility = Utility::factory()->create();

        $this->assertTrue($utility->canBeDeleted());
    }

    #[Test]
    public function can_be_deleted_returns_false_when_fields_use_utility()
    {
        $utility = Utility::factory()->create();
        $field = Field::factory()->create();
        $utility->fields()->attach($field);

        $this->assertFalse($utility->canBeDeleted());
    }

    #[Test]
    public function fields_count_attribute_returns_correct_count()
    {
        $utility = Utility::factory()->create();
        $fields = Field::factory()->count(3)->create();

        $utility->fields()->attach($fields);

        $this->assertEquals(3, $utility->fields_count);
    }

    #[Test]
    public function activate_method_sets_utility_to_active()
    {
        $utility = Utility::factory()->create(['is_active' => false]);

        $result = $utility->activate();

        $this->assertTrue($result);
        $this->assertTrue($utility->fresh()->is_active);
    }

    #[Test]
    public function deactivate_method_sets_utility_to_inactive()
    {
        $utility = Utility::factory()->create(['is_active' => true]);

        $result = $utility->deactivate();

        $this->assertTrue($result);
        $this->assertFalse($utility->fresh()->is_active);
    }

    #[Test]
    public function toggle_status_method_toggles_active_status()
    {
        $utility = Utility::factory()->create(['is_active' => true]);

        $result = $utility->toggleStatus();

        $this->assertTrue($result);
        $this->assertFalse($utility->fresh()->is_active);

        $utility->toggleStatus();
        $this->assertTrue($utility->fresh()->is_active);
    }

    #[Test]
    public function utility_uses_soft_deletes()
    {
        $utility = Utility::factory()->create();
        $utilityId = $utility->id;

        $utility->delete();

        $this->assertSoftDeleted('utilities', ['id' => $utilityId]);
        $this->assertNotNull($utility->fresh()->deleted_at);
    }

    #[Test]
    public function utility_name_is_required_for_mass_assignment()
    {
        $this->expectException(\Illuminate\Database\QueryException::class);

        Utility::create([
            'description' => 'Test description',
            'hourly_rate' => 25.00,
            'is_active' => true,
        ]);
    }

    #[Test]
    public function utility_has_reservations_relationship()
    {
        $utility = Utility::factory()->create();
        $reservation = \App\Models\Reservation::factory()->create();

        // Test the relationship exists
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsToMany::class, $utility->reservations());
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $utility->reservations);
    }

    #[Test]
    public function can_attach_reservation_to_utility_with_pivot_data()
    {
        $utility = Utility::factory()->create([
            'name' => 'Test Utility',
            'hourly_rate' => 25.00,
            'is_active' => true,
        ]);

        $reservation = \App\Models\Reservation::factory()->create();

        // Attach reservation with pivot data
        $utility->reservations()->attach($reservation->id, [
            'hours' => 3,
            'rate' => 25.00,
            'cost' => 75.00,
        ]);

        // Verify the relationship
        $this->assertTrue($utility->reservations->contains($reservation));
        $this->assertEquals(1, $utility->reservations->count());

        // Verify pivot data
        $pivotData = $utility->reservations->first()->pivot;
        $this->assertEquals(3, $pivotData->hours);
        $this->assertEquals(25.00, $pivotData->rate);
        $this->assertEquals(75.00, $pivotData->cost);
    }

    #[Test]
    public function utility_can_have_multiple_reservations()
    {
        $utility = Utility::factory()->create(['hourly_rate' => 25.00]);
        $reservation1 = \App\Models\Reservation::factory()->create();
        $reservation2 = \App\Models\Reservation::factory()->create();

        // Attach multiple reservations
        $utility->reservations()->attach([
            $reservation1->id => ['hours' => 2, 'rate' => 25.00, 'cost' => 50.00],
            $reservation2->id => ['hours' => 3, 'rate' => 25.00, 'cost' => 75.00],
        ]);

        // Verify both reservations are attached
        $this->assertEquals(2, $utility->reservations->count());
        $this->assertTrue($utility->reservations->contains($reservation1));
        $this->assertTrue($utility->reservations->contains($reservation2));
    }

    #[Test]
    public function utility_reservations_count_attribute()
    {
        $utility = Utility::factory()->create();
        $reservations = \App\Models\Reservation::factory()->count(3)->create();

        // Attach reservations
        foreach ($reservations as $reservation) {
            $utility->reservations()->attach($reservation->id, [
                'hours' => 2,
                'rate' => 25.00,
                'cost' => 50.00,
            ]);
        }

        // Test reservations count (similar to fields_count)
        $this->assertEquals(3, $utility->reservations()->count());
    }

    #[Test]
    public function can_be_deleted_should_consider_reservations()
    {
        $utility = Utility::factory()->create();

        // Initially can be deleted (no fields or reservations)
        $this->assertTrue($utility->canBeDeleted());

        // Add a reservation
        $reservation = \App\Models\Reservation::factory()->create();
        $utility->reservations()->attach($reservation->id, [
            'hours' => 2,
            'rate' => 25.00,
            'cost' => 50.00,
        ]);

        // Note: Current implementation only checks fields, not reservations
        // This test documents current behavior - might need enhancement
        $this->assertTrue($utility->canBeDeleted()); // Still true because it only checks fields

        // Add a field to test existing logic
        $field = \App\Models\Field::factory()->create();
        $utility->fields()->attach($field);

        $this->assertFalse($utility->canBeDeleted());
    }

    #[Test]
    public function soft_delete_removes_utility_from_reservations()
    {
        $utility = Utility::factory()->create();
        $reservation = \App\Models\Reservation::factory()->create();

        // Attach utility to reservation
        $utility->reservations()->attach($reservation->id, [
            'hours' => 2,
            'rate' => 25.00,
            'cost' => 50.00,
        ]);

        $this->assertEquals(1, $utility->reservations->count());

        // Soft delete the utility
        $utility->delete();

        // Verify the relationship is removed due to cascade delete in migration
        $reservation->refresh();
        $this->assertEquals(0, $reservation->utilities->count());
    }

    #[Test]
    public function soft_deleted_utility_can_be_restored()
    {
        $utility = Utility::factory()->create(['name' => 'Test Utility']);

        // Soft delete
        $utility->delete();
        $this->assertTrue($utility->trashed());

        // Restore
        $utility->restore();
        $this->assertFalse($utility->trashed());
        $this->assertEquals('Test Utility', $utility->name);
    }

    #[Test]
    public function utility_has_correct_fillable_attributes()
    {
        $utility = new Utility;
        $fillable = $utility->getFillable();

        $expectedFillable = [
            'name',
            'description',
            'icon_class',
            'hourly_rate',
            'is_active',
        ];

        foreach ($expectedFillable as $attribute) {
            $this->assertContains($attribute, $fillable);
        }

        $this->assertCount(5, $fillable);
    }

    #[Test]
    public function utility_has_correct_casts()
    {
        $utility = Utility::factory()->create([
            'is_active' => 1,
            'hourly_rate' => '25.50',
        ]);

        // Test boolean casting
        $this->assertIsBool($utility->is_active);
        $this->assertTrue($utility->is_active);

        // Test decimal casting (Laravel decimal cast returns string, not float)
        $this->assertIsString($utility->hourly_rate);
        $this->assertEquals('25.50', $utility->hourly_rate);
    }

    #[Test]
    public function formatted_hourly_rate_handles_null_values()
    {
        $utility = Utility::factory()->create(['hourly_rate' => null]);
        $this->assertEquals('N/A', $utility->formatted_hourly_rate);
    }

    #[Test]
    public function formatted_hourly_rate_formats_correctly()
    {
        $testCases = [
            '25.00' => '$25.00',
            '25.50' => '$25.50',
            '0.00' => '$0.00',
            '999.99' => '$999.99',
            '1000.00' => '$1,000.00',
        ];

        foreach ($testCases as $rate => $expected) {
            $utility = Utility::factory()->create(['hourly_rate' => $rate]);
            $this->assertEquals($expected, $utility->formatted_hourly_rate);
        }
    }

    #[Test]
    public function display_name_handles_different_statuses()
    {
        $activeUtility = Utility::factory()->create([
            'name' => 'Active Utility',
            'is_active' => true,
        ]);

        $inactiveUtility = Utility::factory()->create([
            'name' => 'Inactive Utility',
            'is_active' => false,
        ]);

        $this->assertEquals('Active Utility', $activeUtility->display_name);
        $this->assertEquals('Inactive Utility (Inactive)', $inactiveUtility->display_name);
    }



    #[Test]
    public function activate_method_sets_is_active_to_true()
    {
        $utility = Utility::factory()->create(['is_active' => false]);

        $result = $utility->activate();

        $this->assertTrue($result);
        $this->assertTrue($utility->fresh()->is_active);
    }

    #[Test]
    public function deactivate_method_sets_is_active_to_false()
    {
        $utility = Utility::factory()->create(['is_active' => true]);

        $result = $utility->deactivate();

        $this->assertTrue($result);
        $this->assertFalse($utility->fresh()->is_active);
    }

    #[Test]
    public function toggle_status_method_switches_active_state()
    {
        $utility = Utility::factory()->create(['is_active' => true]);

        // Toggle from active to inactive
        $result = $utility->toggleStatus();
        $this->assertTrue($result);
        $this->assertFalse($utility->fresh()->is_active);

        // Toggle back to active
        $result = $utility->toggleStatus();
        $this->assertTrue($result);
        $this->assertTrue($utility->fresh()->is_active);
    }

    #[Test]
    public function scopes_work_with_mixed_active_states()
    {
        // Create utilities with different states
        $activeUtilities = Utility::factory()->count(3)->create(['is_active' => true]);
        $inactiveUtilities = Utility::factory()->count(2)->create(['is_active' => false]);

        // Test active scope
        $foundActive = Utility::active()->get();
        $this->assertEquals(3, $foundActive->count());
        foreach ($activeUtilities as $utility) {
            $this->assertTrue($foundActive->contains($utility));
        }

        // Test inactive scope
        $foundInactive = Utility::inactive()->get();
        $this->assertEquals(2, $foundInactive->count());
        foreach ($inactiveUtilities as $utility) {
            $this->assertTrue($foundInactive->contains($utility));
        }
    }



    #[Test]
    public function fields_relationship_works_with_timestamps()
    {
        $utility = Utility::factory()->create();
        $field = \App\Models\Field::factory()->create();

        // Attach field to utility
        $utility->fields()->attach($field);

        // Verify relationship with timestamps
        $attachedField = $utility->fields->first();
        $this->assertEquals($field->id, $attachedField->id);
        $this->assertNotNull($attachedField->pivot->created_at);
        $this->assertNotNull($attachedField->pivot->updated_at);
    }

    #[Test]
    public function can_be_deleted_logic_is_accurate()
    {
        $utility = Utility::factory()->create();

        // Initially can be deleted (no fields)
        $this->assertTrue($utility->canBeDeleted());
        $this->assertEquals(0, $utility->fields_count);

        // Add a field
        $field = \App\Models\Field::factory()->create();
        $utility->fields()->attach($field);

        // Now cannot be deleted
        $this->assertFalse($utility->canBeDeleted());
        $this->assertEquals(1, $utility->fields_count);

        // Remove the field
        $utility->fields()->detach($field);

        // Can be deleted again
        $this->assertTrue($utility->canBeDeleted());
        $this->assertEquals(0, $utility->fields_count);
    }
}
